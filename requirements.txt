# Trading Bot Server Requirements

pandas_ta
schedule
supabase
httpx

# Core dependencies
asyncio-mqtt==0.16.1
asyncpg==0.29.0
numpy==1.24.3
pandas==2.0.3
TA-Lib==0.4.28

# Database and async support
psycopg2-binary==2.9.7
sqlalchemy==2.0.19
alembic==1.11.1

# HTTP and API
aiohttp==3.8.5
requests==2.31.0
websockets==11.0.3

# Data processing and analysis
scipy==1.11.1
scikit-learn==1.3.0

# Logging and monitoring
structlog==23.1.0
prometheus-client==0.17.1

# Configuration and environment
python-dotenv==1.0.0
pydantic==2.1.1
pydantic-settings==2.0.2

# Date and time handling
python-dateutil==2.8.2
pytz==2023.3

# Testing (optional)
pytest==7.4.0
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# Development tools (optional)
black==23.7.0
flake8==6.0.0
mypy==1.5.1

# Performance monitoring
memory-profiler==0.60.0
psutil==5.9.5

# Crypto and trading specific
ccxt==4.0.77
python-binance==1.0.19

# JSON and data serialization
orjson==3.9.4
msgpack==1.0.5

# Caching (optional)
redis==4.6.0
aioredis==2.0.1

# Security
cryptography==41.0.3
bcrypt==4.0.1

# Utilities
click==8.1.6
rich==13.5.2
tqdm==4.65.0
